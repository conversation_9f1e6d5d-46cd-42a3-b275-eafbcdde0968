<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * enrol plugin Stripe payment subscription.
 *
 * schedule task defination
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$tasks = array(
    array(
        'classname' => '\enrol_stripepaymentpro\task\enrol_stripepaymentpro_unenrol_task',
        'blocking' => 0,
        'minute' => '0',
        'hour' => '0', // every midnight
        'day' => '*',
        'dayofweek' => '*',
        'month' => '*',
        'disabled' => 0
    ),
    array(
        'classname' => '\enrol_stripepaymentpro\task\classes\enrol_stripepaymentpro_check_license',
        'blocking' => 0,
        'minute' => '0',
        'hour' => '0', // Run at midnight
        'day' => '*',
        'dayofweek' => '*',
        'month' => '*',
        'disabled' => 0
    )
);
