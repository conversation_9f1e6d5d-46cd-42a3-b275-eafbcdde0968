<?php

// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe enrolment plugin.
 *
 * helper file for event listner in webhook
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace enrol_stripepaymentpro\helper;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot . '/enrol/stripepayment/Stripe/init.php');

/**
 * Stripe enrolment plugin.
 *
 * helper class for event listner in webhook
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class webhook_helper {

    private $plugincore;
    private $plugin;
    private $sessionid;
    private $stripeclient;

	/**
	 * constructor
	 */
    public function __construct($sessionid) {
        $this->plugin = enrol_get_plugin('stripepaymentpro');
        $this->plugincore = enrol_get_plugin('stripepayment');
        $this->sessionid = $sessionid;
        $this->stripeclient = new \Stripe\StripeClient(get_config('enrol_stripepayment', 'secretkey'));
    }

    /**
     * After successful payment by stripe, enroll the user to the course
     * and collect all the data and submit it to the database.
     */
    public function successfull_enrolment() {
        global $DB, $CFG, $PAGE, $OUTPUT;

        $session = $this->stripeclient->checkout->sessions->retrieve($this->sessionid);
        $instanceid = $session->metadata->instanceid;
        $userid = $session->metadata->userid;
		
        if ($session->mode == 'payment') {
            $lineItems = $this->stripeclient->checkout->sessions->allLineItems($session->id, ['limit' => 1]);
            $product = $this->stripeclient->products->retrieve($lineItems->data[0]->price->product);
            $paymentIntent = $this->stripeclient->paymentIntents->retrieve($session->payment_intent);
            $paymentmethod = $paymentIntent->payment_method;
        } else {
            // Retrieve details from strippe from sessionid we get .
            $subscription = $this->stripeclient->subscriptions->retrieve($session->subscription);
            $customer = $this->stripeclient->customers->retrieve($subscription->customer);
            $paymentmethod = $this->stripeclient->paymentMethods->retrieve($subscription->default_payment_method);
            $product = $this->stripeclient->products->retrieve($subscription->items->data[0]->price->product);
        }


    	// Create the object for insert data to database.
        $data = new \stdClass();
        $data->coupon_id = $session->metadata->couponid;
        $data->stripeEmail = $session->mode == 'subscription' ? $customer->email : $session->customer_email;
        $data->currency = $session->currency;
        $data->receiver_id = isset($subscription) ? $subscription->customer : null;
    	
    	// Get the plugin instance from enrol table.
        $plugininstance = $DB->get_record("enrol", array("id" => $instanceid, "status" => 0));

        $data->courseid = $plugininstance->courseid;
        $data->instanceid = $instanceid;
        $data->userid = (int)$userid;
        $data->timeupdated = time();
        if ($session->mode == 'subscription') { 
            $data->renewal_interval = $subscription->items->data[0]->price->recurring->interval;
            $data->renewal_intervalperiod = $subscription->items->data[0]->price->recurring->interval_count;
            $data->trialperiodend = $subscription->items->data[0]->price->recurring->trial_period_days;
            $data->subscription_id = $subscription->id;
            $data->pending_reason = $paymentmethod->last_payment_error ? $paymentmethod->last_payment_error : 'NA';
        }
        $data->product_id = $product->id;
        $data->product_name = $product->name;
        $data->product_type = $session->mode == 'subscription' ? $product->type : 'one_time';
        $data->receiver_email = $session->mode == 'subscription' ? $customer->email : $session->customer_email;
        $data->txn_id = isset($subscription) ? $subscription->default_payment_method : $paymentmethod;
        $data->tax = $session->amount_total / 100;
        $data->memo = isset($subscription) ?  $subscription->default_payment_method : $paymentmethod;
        $data->payment_status = $session->payment_status;
        $data->item_name = $product->name;
    	
        $user = $DB->get_record("user", array("id" => $data->userid));
        $course = $DB->get_record("course", array("id" => $data->courseid));
        $context = \context_course::instance($course->id, IGNORE_MISSING);

       
        $PAGE->set_context($context);
        $DB->insert_record("enrol_stripepaymentpro", $data);

        if ($plugininstance->enrolperiod) {
            $timestart = time();
            $timeend = $timestart + $plugininstance->enrolperiod;
        } else {
            $timestart = 0;
            $timeend = 0;
        }

    	// enrol the user to the course.
        $this->plugin->enrol_user($plugininstance, $user->id, $plugininstance->roleid, $timestart, $timeend);

        if ($users = get_users_by_capability($context, 'moodle/course:update', 'u.*', 'u.id ASC', '', '', '', '', false, true)) {
            $users = sort_by_roleassignment_authority($users, $context);
            $teacher = array_shift($users);
        } else {
            $teacher = false;
        }

    	//get the config we set in stripepayment plugin abou mail admin, user, teacher
        $mailstudents = get_config('enrol_stripepayment', 'mailstudents');
        $mailteachers = get_config('enrol_stripepayment', 'mailteachers');
        $mailadmins = get_config('enrol_stripepayment', 'mailadmins');
        $shortname = format_string($course->shortname, true, array('context' => $context));
        $coursecontext = \context_course::instance($course->id);
        $orderdetails = new \stdClass();
        $orderdetails->coursename = format_string($course->fullname, true, array('context' => $coursecontext));

        if ($mailstudents) {
            $orderdetails->profileurl = "$CFG->wwwroot/user/view.php?id=$user->id";
            $userfrom = empty($teacher) ? \core_user::get_support_user() : $teacher;
            $subject = get_string("enrolmentnew", 'enrol', $shortname);
            $fullmessage = get_string('welcometocoursetext', '', $orderdetails);
            $fullmessagehtml = html_to_text('<p>' . get_string('welcometocoursetext', '', $orderdetails) . '</p>');
            email_to_user($user, $userfrom, $subject, $fullmessage, $fullmessagehtml);
        }

        if (!empty($mailteachers) && !empty($teacher)) {
            $orderdetails->user = fullname($user);
            $subject = get_string("enrolmentnew", 'enrol', $shortname);
            $fullmessage = get_string('enrolmentnewuser', 'enrol', $orderdetails);
            $fullmessagehtml = html_to_text('<p>' . get_string('enrolmentnewuser', 'enrol', $orderdetails) . '</p>');
            email_to_user($teacher, $user, $subject, $fullmessage, $fullmessagehtml);
        }

        if (!empty($mailadmins)) {
            $orderdetails->user = fullname($user);
            $admins = get_admins();
            foreach ($admins as $admin) {
                $subject = get_string("enrolmentnew", 'enrol', $shortname);
                $fullmessage = get_string('enrolmentnewuser', 'enrol', $orderdetails);
                $fullmessagehtml = html_to_text('<p>' . get_string('enrolmentnewuser', 'enrol', $orderdetails) . '</p>');
                email_to_user($admin, $user, $subject, $fullmessage, $fullmessagehtml);
            }
        }
    }

    /**
     * If the subscription is stopped and the end date of the current subscription
     * has passed, then the user should be unenrolled.
     */
    public function unenrol_user_on_subscription_deleted($subscriptionid) {
        // Implementation for unenrolling users when the subscription deletes.
    	global $DB;
    	$subscription = $this->stripeclient->subscriptions->retrieve($subscriptionid);
    	$unenroleuserdata = $DB->get_records('enrol_stripepaymentpro', array('subscription_id' => $subscriptionid));
    	$currentperiodend = $subscription->current_period_end;
        $currentdate = time();
    	if ($subscription->status == 'canceled' && $currentperiodend < $currentdate) {
    		$instance = $DB->get_record('enrol', array('id'=>$unenroleuserdata->instanceid, "enrol" => "stripepaymentpro"), '*', MUST_EXIST);
    		$this->$plugin->unenrol_user($instance, $unenroleuserdata->userid);
        }
    }

}
