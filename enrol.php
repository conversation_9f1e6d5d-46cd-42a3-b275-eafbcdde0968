<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Listens for Instant Payment Notification from Stripe
 *
 * This script waits for Payment notification from <PERSON><PERSON>,
 * then double checks that data by sending it back to Stripe.
 * If Stripe verifies this then it sets up the enrolment for that
 * user.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

global $CFG, $PAGE;
require_once($CFG->libdir . '/enrollib.php');    
require_once($CFG->dirroot . '/enrol/stripepayment/Stripe/init.php');
use \Stripe\Stripe as Stripe;
use \Stripe\Price as Price;
use \Stripe\Product as Product;
use \Stripe\Coupon as Coupon;
// get currency symbal
$plugincorecore = enrol_get_plugin('stripepayment');
$currency_symbol = $plugincorecore->show_currency_symbol(strtolower($instance->currency));
$plugin = enrol_get_plugin('stripepaymentpro');
$enable_coupon_section = !empty(get_config('enrol_stripepayment', 'enable_coupon_section')) ? true : false;

$enrolbtncolor = get_config('enrol_stripepayment', 'enrolbtncolor');
Stripe::setApiKey(get_config('enrol_stripepayment', 'secretkey'));
$product = Product::retrieve($productid);
$price = Price::retrieve($product->default_price);
$sign_up_fee = $price->unit_amount/$plugin->get_fractional_unit_amount($instance->currency);

if ($instance->customtext4 > 0) {
    $renewalcostobject = Price::retrieve($instance->customtext4);
    $renewal_fee = $renewalcostobject->unit_amount/$plugin->get_fractional_unit_amount($instance->currency);
    $interval = $renewalcostobject->recurring->interval;
    $interval_count = $renewalcostobject->recurring->interval_count;
    $trial_period_days = $instance->customint2 ? $instance->customint2 / 86400 - 1:0;
}


$coupon_information = optional_param('coupon_information', null, PARAM_RAW);
$coupon_name = optional_param('coupon_name', null, PARAM_RAW);
$couponid = $coupon_discount_message = "";

$coupon_details = [];
$coupon_subtotal_off = $coupon_fixed_off = $coupon_percentage_off = 0;
if(isset($coupon_information) && $coupon_information != "") {
    $coupon_details = json_decode($coupon_information);
    $couponid = $coupon_details->couponid;

    if(isset($coupon_details->amount_off) && $coupon_details->amount_off > 0) {
        $coupon_fixed_off = $coupon_details->amount_off / $plugin->get_fractional_unit_amount($instance->currency);
        $coupon_discount_message = "Fixed " . $currency_symbol . $coupon_fixed_off . " off";
    }
    if(isset($coupon_details->percent_off) && $coupon_details->percent_off > 0) {
        $coupon_percentage_off = $coupon_details->percent_off;
        $coupon_discount_message = $coupon_details->percent_off . "% off";
    }
    if(isset($coupon_details->duration) && $coupon_details->duration != "") $coupon_discount_message .= ($coupon_details->duration == "repeating") ? " for " . $coupon_details->no_of_months . " months" : " " . $coupon_details->duration;
}

$course = $DB->get_record('course', array('id' => $instance->courseid));

if(isset($trial_period_days) && $trial_period_days > 0) $total_cost = $sign_up_fee;
else $total_cost = $instance->customtext4 > 0 ? $renewal_fee + $sign_up_fee : $sign_up_fee;

if($coupon_fixed_off > 0) $coupon_subtotal_off = $coupon_fixed_off;
else if($coupon_percentage_off > 0) $coupon_subtotal_off = $total_cost * $coupon_percentage_off /100;
$total_cost = $total_cost - $coupon_subtotal_off;
$payment_gateway_type = get_config('enrol_stripepaymentpro', 'payment_gateway_type');
$today = new DateTime();

// Calculate the next recurring date based on the interval and interval count
$intervalspec = 'P';
if (isset($interval_count)) {
    switch ($interval) {
        case 'day':
            $intervalspec .= $interval_count . 'D';
            break;
        case 'week':
            $intervalspec .= ($interval_count * 7) . 'D';
            break;
        case 'month':
            $intervalspec .= $interval_count . 'M';
            break;
        case 'year':
            $intervalspec .= $interval_count . 'Y';
            break;
        default:
            throw new Exception('Invalid interval type');
    }

}

if ($instance->customtext4 > 0 ) {
    $intervalobject = new DateInterval($intervalspec);
    $nextrecurringdate = clone $today;
    if($trial_period_days > 0) $nextrecurringdate->add(new DateInterval( 'P' . ($trial_period_days + 1) . 'D'));
    else $nextrecurringdate->add($intervalobject);
    $nextrecurringdateformatted = $nextrecurringdate->format('Y-m-d');
}
?>
<script src="https://js.stripe.com/v3/"></script>
<div class="strip-wrap">
    <div class="stripe-right">
        <p class='stripe-dclr'>
            <?php print_string("paymentrequired") ?>
        </p>
        <div class="stripe-img">
            <img src="<?php echo $CFG->wwwroot; ?>/enrol/stripepaymentpro/pix/stripe-payment.png">
        </div>
        <!-- coupon -->
        <?php if ($enable_coupon_section) { ?>
            <div class="couponcode-wrap">
                <span class="couponcode-text">
                    <?php echo get_string("couponcodedescription", "enrol_stripepaymentpro"); ?>
                </span>
                <p class="stripe-cupon-input">
                    <input type=text id="coupon" />
                    <button id="apply" class="stripe-cupon-apply">
                        <?php echo get_string("applycode", "enrol_stripepaymentpro"); ?>
                    </button>
                </p>
                <div id="new_coupon"></div>
            </div>
        <?php } ?>
        <!-- cost -->
        <form id="form_data_new" action="" method="post">
            <input id="form_data_new_data" type="hidden" name="coupon_information" value="" />
            <input id="form_data_new_coupon_name" type="hidden" name="coupon_name" value="" />
        </form>

        <div style="flex: auto;">
            <h2><?php get_string("course", "enrol_stripepaymentpro") ?></h2>
            <h2><?php get_string("subtotal", "enrol_stripepaymentpro") ?></h2>
        </div>
        <div class="paydetail">
            <div class="stripe-cost-row">
                <div class="heading_table">
                </div>
                <div class="strip-subcription-plan-details details-content">
        			<p>
                    <?php
                    if ($instance->customtext4 > 0 ) {
                        echo get_string("subscription_plan", "enrol_stripepaymentpro") . ":</p><span>{$currency_symbol}{$renewal_fee} for {$interval_count} {$interval} with {$trial_period_days} day free trial and a {$currency_symbol}" ; echo $sign_up_fee; echo "  sign-up fee</span>"; 
                    }?>
                </div>
                <div class="strip-subcription-plan-details">
                    <p><?php echo get_string("subtotal", "enrol_stripepaymentpro") . ":</p><span>{$currency_symbol}{$total_cost} </span>"; ?>
                </div>
            </div>
            <?php if($coupon_subtotal_off > 0) { ?>
            <div class='strip-subcription-plan-details'>
            <?php echo "<p>" . get_string("couponapplied", "enrol_stripepaymentpro") . ":</p><span> - {$currency_symbol}{$coupon_subtotal_off} [<i>{$coupon_name}</i>] </span>"; ?>
            </div>
            <div class='strip-subcription-plan-details'><?php echo "<p></p><span>{$coupon_discount_message}</span>"; ?></div>
           <?php } ?>
            <div id="reload">
                <div class="stripe-buy-btn">
                    <?php echo "";?>
                </div>
               
                <!-- subcriptioninfo -->
                <div class="subcriptioninfo">
                    <?php 
                    if ($instance->customtext4 > 0 ) {
                        if ($trial_period_days) { ?>
                            <div class="stripe-line-row">
                                <div class="strip-subcription-plan-details">
                                    <p><?php echo get_string("trialperiod", "enrol_stripepaymentpro") . ":</p><span> {$trial_period_days} days</span>"; ?>
                                </div>
                            </div>
                        <?php } ?>
                        <div class="recurringtotal">
                            <div class="stripe-line-row">
                                <div class="strip-subcription-plan-details">
                                    <p><?php echo get_string("recurringtotal", "enrol_stripepaymentpro") . ":</p><span>{$currency_symbol}{$renewal_fee} for {$interval_count} {$interval} </span>"; ?>
                                </div>
                            </div>
                            <div class="stripe-line-row">
                                <div class="strip-subcription-plan-details">
                                    <p><?php echo get_string("next_renewal", "enrol_stripepaymentpro") ?>:</p><?php echo $nextrecurringdateformatted ?>
                                </div>
                            </div>
                        </div> <?php
                    } ?>
                </div>
                
                <!-- LOOP -->
                <div id="paymentResponse" class="stripe-buy-btn">
                    <div id="buynow">
                        <button class="stripe-button" id="payButton">
                            <?php echo get_string("buy_now", "enrol_stripepaymentpro"); ?>
                        </button>
                    </div>
                    <!-- LOOP -->
                
                <div class="stripe-container">
                    <form id="payment-form">
                        <div id="payment-element">
                            <!-- Stripe payment element will be inserted here -->
                        </div>
                    </form>
                    <div id="payment-message" class="hidden"></div>
                    <div id="errror-log"></div>
                </div>
            </div>
            <?php $PAGE->requires->js_call_amd('enrol_stripepaymentpro/stripe_payment_pro', 'stripe_payment_pro', array($payment_gateway_type, $USER->id, get_config('enrol_stripepayment', 'publishablekey'), $coupon_name, $couponid, $instance->id, get_string("please_wait", "enrol_stripepaymentpro"), get_string("buy_now", "enrol_stripepaymentpro"), get_string("invalidcouponcode", "enrol_stripepaymentpro"))); ?>
        </div>
    </div>
    
    <style>
        button#apply {
        background-color:
            <?php echo $enrolbtncolor; ?>
        ;
    }
    button#payButton,
    button#card-button-zero {
        background-color:
            <?php echo $enrolbtncolor; ?>
        ;
    }
    </style>
