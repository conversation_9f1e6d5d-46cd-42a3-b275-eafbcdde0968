<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
/**
 * Course wise edit form.
 *
 * Adds new instance of enrol_stripepaymentpro to specified course
 * or edits current instance.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
defined('MOODLE_INTERNAL') || die();
global $CFG, $DB;
require_once($CFG->libdir.'/formslib.php');


class enrol_stripepaymentpro_coupon_form extends moodleform {
    // Add elements to form.
    public function definition() {
        $plugincore = enrol_get_plugin('stripepayment');
        $plugin = enrol_get_plugin('stripepaymentpro');
        // A reference to the form is stored in $this->form.
        // A common convention is to store it in a variable, such as `$mform`.
        $mform = $this->_form; // Don't forget the underscore!
        list($stripe_course_list, $plugin, $plugincore) = $this->_customdata;
        
        // Coupon Name
        $mform->addElement('text', 'coupon_name', get_string('coupon_name', 'enrol_stripepaymentpro'));
        $mform->setType('coupon_name', PARAM_RAW);
        $mform->addRule('coupon_name', get_string('coupon_name_required', 'enrol_stripepaymentpro'), 'required', null);
        $mform->setDefault('coupon_name', '');

        //Coupon Type
        $coupon_types = ['amount_off' => get_string('discount_type_fixed', 'enrol_stripepaymentpro'),
                         'percent_off' => get_string('discount_type_percentage', 'enrol_stripepaymentpro')];

        $mform->addElement('select', 'coupon_types', get_string('coupon_types', 'enrol_stripepaymentpro'), $coupon_types);
        $mform->setDefault('coupon_types', get_config('enrol_stripepaymentpro', 'coupon_types'));
        

        // Coupon amount
        $mform->addElement('text', 'discount_amount', get_string('discount_amount', 'enrol_stripepaymentpro'));
        $mform->setType('discount_amount', PARAM_FLOAT);
        $mform->addRule('discount_amount', get_string('discount_amount_non_zero', 'enrol_stripepaymentpro'), 'nonzero', null);
        $mform->setDefault('discount_amount', 0);

        // Coupon currency
        $coupon_currencies = $plugincore->get_currencies();    
        $mform->addElement('select', 'coupon_currency', get_string('coupon_currency', 'enrol_stripepaymentpro'), $coupon_currencies);
        $mform->setDefault('coupon_currency', get_config('enrol_stripepaymentpro', 'coupon_currency'));

        // Coupon Duration
        $coupon_durations = ['forever' => get_string('coupon_duration_forever', 'enrol_stripepaymentpro'),
                            'once' => get_string('coupon_duration_once', 'enrol_stripepaymentpro'),
                            'repeating' => get_string('coupon_duration_multiple_months', 'enrol_stripepaymentpro'),
                     ];

        $mform->addElement('select', 'coupon_duration', get_string('coupon_duration', 'enrol_stripepaymentpro'), $coupon_durations);
        $mform->setDefault('coupon_duration', get_config('enrol_stripepaymentpro', 'coupon_duration'));

        $mform->addElement('text', 'coupon_duration_multiple_months_val', get_string('coupon_duration_multiple_months_val', 'enrol_stripepaymentpro'), array('size' => 2));
        $mform->setType('coupon_duration_multiple_months_val', PARAM_INT);
        $mform->setDefault('coupon_duration_multiple_months_val', 1);
        $mform->addRule('coupon_duration_multiple_months_val', get_string('coupon_duration_multiple_months_val_non_zero', 'enrol_stripepaymentpro'), 'nonzero', null);
        $mform->hideIf('coupon_duration_multiple_months_val', 'coupon_duration', 'ne', 'repeating');
        // $mform->setDefault('coupon_duration_multiple_months_val', $plugin->get_config('coupon_duration_multiple_months_val'));
        

        // Coupon expiry date
        $mform->addElement('date_time_selector', 'coupon_expiry', get_string('coupon_expiry', 'enrol_stripepaymentpro'), array('optional' => true));
        $mform->setDefault('coupon_expiry', 0);
        

        // Coupon course assignment
        $stripe_course_list['0'] = get_string('coupon_for_all_courses', 'enrol_stripepaymentpro');
        $mform->addElement('select', 'coupon_course_assignment', get_string('coupon_course_assignment', 'enrol_stripepaymentpro'), $stripe_course_list);
        $mform->setDefault('coupon_course_assignment', 0);

        // When ready, add your action buttons.
        $this->add_action_buttons();
    }

    // Custom validation should be added here.
    function validation($data, $files) {
        $errors = parent::validation($data, $files);
        if ($data["coupon_name"] == "") {
            $errors['couponnameblank'] = 'Invalid input for coupon name';
        }
        if ($data["coupon_expiry"] > 0 && $data["coupon_expiry"] <= time()) {
            $errors['invalidcouponexpiry'] = 'Invalid input for coupon expiry';
        } 
        if ($data["discount_amount"] <= 0) {
            $errors['invalidcouponamount'] = 'Invalid input for coupon amount';
        } else if ($data["discount_amount"] > 100 && $data["coupon_types"] == "percent_off") {
            $errors['invaliddiscountpercentage'] = 'Coupon percentage would be less than 100';
        } 
        return $errors;
    }
}