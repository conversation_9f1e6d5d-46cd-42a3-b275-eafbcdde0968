define(['jquery', 'core/ajax'], function($, ajax) {
    return {
        // javascript for coupon setting 
        initCouponSettings: function() {
            $(document).ready(function() {
                // Deactivate individual coupon
                $(document).on('click', '.deactivate-coupon', function() {
                    var courseid = $(this).data('courseid');
                    var couponid = $(this).data('couponid');
                    console.log("courseid" + courseid + "  couponid" + couponid);
                    if (confirm('Are you sure you want to deactivate the coupon?')) {
                        ajax.call([{
                            methodname: 'moodle_stripepaymentpro_deactivate_coupon',
                            args: { courseid: courseid, couponid: couponid }
                        }])[0].then(function (response) {
                            if (response) {
                                console.log(response);
                                alert('Coupon deactivated successfully!');
                                location.reload();
                            } else {
                                console.log(response);
                                alert('Failed to deactivate coupon!');
                            }
                        }).fail(function (ex) {
                            console.log(ex);
                            alert('Error occurred while deactivating coupon!');
                        });
                    }
                });

                // Deactivate all coupons for a course
                $(document).on('click', '.deactivate-all-coupons', function() {
                    var courseid = $(this).data('courseid');
                    console.log(courseid);
                    if (confirm('Are you sure you want to deactivate the coupon?')) {
                        ajax.call([{
                            methodname: 'moodle_stripepaymentpro_deactivate_all_coupons',
                            args: { courseid: courseid }
                        }])[0].then(function (response) {
                            if (response) {
                                alert('All coupons deactivated successfully!');
                                location.reload();
                            } else {
                                alert('Failed to deactivate all coupons!');
                            }
                        }).fail(function (ex) {
                            console.log(ex);
                            alert('Error occurred while deactivating all coupons!');
                        });
                    }
                });

            
            	$('#all_coupons_section').show();
    			$('#generate_coupon_section').hide();

    			// Toggle sections on button click
    			$('#all_coupon_button').click(function () {
        			$('#all_coupons_section').show();
        			$('#generate_coupon_section').hide();
                	$(this).addClass('active');
                	$('#generate_coupons_button').removeClass('active');
    			});

    			$('#generate_coupons_button').click(function () {
        			$('#generate_coupon_section').show();
        			$('#all_coupons_section').hide();
                	$(this).addClass('active');
                	$('#all_coupon_button').removeClass('active');
    			});
            });
        },
        stripe_payment_pro: function(payment_gateway_type, user_id, publishablekey, couponid, instance_id, please_wait_string, buy_now_string, invalid_code_string) {
            $('#apply').click(function() {
                var coupon_id_name = $("#coupon").val();
                var promises = ajax.call([{
                    methodname: 'moodle_stripepaymentpro_couponsettings',
                    args: { couponid: coupon_id_name, instanceid: instance_id }
                }]);

                promises[0].then(function(data) {
                    $("#form_data_new_data").val(data.status);
                    $("#form_data_new_coupon_id").val(coupon_id_name);
                    $("#form_data_new").submit();
                    $("#reload").load(location.href + " #reload");
                    $("#coupon_id").val(coupon_id_name);
                    $(".coupon_id").val(coupon_id_name);
                    if (data == 0.00) {
                        $('#amountgreaterzero').hide();
                        $('#amountequalzero').show();
                    } else {
                        $('#amountgreaterzero').show();
                        $('#amountequalzero').hide();
                    }
                }).fail(function(ex) {
                    console.log(ex);
                    $("#coupon").focus();
                    $("#new_coupon").html('<p style="color:red;"><b>' + invalid_code_string + '</b></p>');
                });
            });

            var get_card_zero_cost = $('#card-button-zero');
            if (get_card_zero_cost) {
                get_card_zero_cost.click(function() {
                    var promises = ajax.call([{
                        methodname: 'moodle_stripepaymentpro_free_enrolsettings',
                        args: { user_id: user_id, couponid: couponid, instance_id: instance_id }
                    }]);
                    promises[0].then(function() {
                        location.reload();
                    }).fail(function() {
                        location.reload();
                    });
                });
            }

            var buyBtn = $('#payButton');
            var responseContainer = $('#paymentResponse');



            var stripe = Stripe(publishablekey);

            var handleResult = function(result) {
                if (result.error) {
                    responseContainer.html('<p>' + result.error.message + '</p>');
                }
                buyBtn.prop('disabled', false).text(buy_now_string);
            };

            //necessery function for checkout 
            const beforePayment = () => {
                return new Promise((resolve, reject) => {
                    let response = ajax.call([{
                        methodname: 'moodle_stripepaymentpro_stripe_js_settings',
                        args: { user_id: user_id, couponid: couponid, instance_id: instance_id }
                    }]);
                    response[0].then((data) => {
                        resolve(data);
                    });
                });
            }

            //// fetch client secret for showing form in side 
            const fetchclientsecret = async() => {
                const response = await beforePayment();
                const data = JSON.parse(response.paymentintent);
                console.log("data", data);
                return data.client_secret;
            }

            const initializeform = async () => {
                if (fetchclientsecret) {
                    console.log("client secret");
                    // console.log(JSON.parse(paymentintent));
                    try {
                        var checkout = await stripe.initEmbeddedCheckout({
                            fetchClientSecret: fetchclientsecret,
                        });
                        console.log(checkout);
                    } catch (error) {
                        console.error(error);
                    }
                    checkout.mount("#payment-element");
                  
                } else {
                    handleResult({ error: { message: "error while geting the client secret" } });
                }
            }
            
            // if element is selected then show form in frontend
            if (payment_gateway_type === 'elements') {
                console.log("elements");
                buyBtn.remove();
                initializeform();
            } else if (payment_gateway_type === 'checkout') {
                if (buyBtn) {
                    buyBtn.click( async function() {
                        buyBtn.prop('disabled', true).text(please_wait_string);
                        const data = await beforePayment();
                        var session = JSON.parse(data.session);
                        if (session.id) {
                            stripe.redirectToCheckout({ sessionId: session.id }).then(handleResult).catch(handleResult);
                        } else {
                            handleResult(data);
                        }
                    });
                }
            }
        }
    };
});