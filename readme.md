Moodle Stripe Pro Enrollment Plugin [enrol_stripepaymentpro]
=======================
* Developed by: Team DualCube
* Copyright: (c) 2023 DualCube <<EMAIL>>
* License: [GNU GENERAL PUBLIC LICENSE](LICENSE)
* Contributors:  DualCube

Description
===========
This plugin helps admins and webmasters use Stripe as the payment gateway. Stripe is one of the popular payment gateways. This plugin has all the settings for development as well as for production usage. It is easy to install, setup and effective.

_Added features include:_
* Complerely SCA Compatible
* Coupon functionality while enrolling students with percent-off or amount-off discount
* Full course name and site logo on stripe checkout
* Ability to set custom stripe transaction statement descriptor for each course
* Admins and Webmasters, now, can create, manage, and keep track of all promotional codes directly in their Stripe dashboard.

Installation
============
1. setup stripe payment plugin.
2. Access Token: Site Administration > Server tab > Web Services > Manage Tokens. select Create Token. [User – Admin, Service – moodle_enrol_stripepaymentpro]. Copy Token ID to Stripe Settings on above stap.
3. Go to Enrolments > Manage enrol plugins > Enable 'Stripe' from list
4. Go to Course administration > Participants > Enrolment methods > Add method 'Stripe pro' from the dropdown. Set 'Custom instance name', 'Enrol cost', 'Currency' etc and add the method.
5. This completes all the steps from the administrator end. Now registered users can login to the Moodle site and view the course after a successful payment.


Requirements
------------
* Moodle 3.0 - 4.2
* Stripe account


How to create Stripe account
--------------
1. Create account at https://stripe.com.
2. Complete your merchant profile details from https://dashboard.stripe.com/account.
3. Now set up secret key and publishers key at https://dashboard.stripe.com/account/apikeys.
4. For test mode use test api keys and for live mode use live api keys.
5. Now you are done with merchant account set up.


Useful links
============
* Moodle Forum: [https://moodle.org/course](https://moodle.org/course)
* Moodle Plugins Directory:  [https://moodle.org/plugins](https://moodle.org/plugins)
* Stripe API: [https://stripe.com/docs/api?lang=php#intro](https://stripe.com/docs/api?lang=php#intro)
* DualCube Contributions: [https://moodle.org/plugins/browse.php?list=contributor&id=1832609](https://moodle.org/plugins/browse.php?list=contributor&id=1832609)


Release history
===============
* **v1.0:** 2023-05-02
